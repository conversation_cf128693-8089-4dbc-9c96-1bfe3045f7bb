import { inject, singleton } from 'tsyringe';

import { IUser, toDbId } from '@malou-io/package-models';
import { ApplicationLanguage, Role, UserCaslRole } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';
import { CreateNewAccountService } from ':modules/users/services/create-new-account.service';
import { UsersRepository } from ':modules/users/users.repository';
import { NewOrganizationEvent } from ':modules/webhooks/malou/validators/new-organization.validators';

@singleton()
export class CreateNewOrganizationUseCase {
    constructor(
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _createNewAccountService: CreateNewAccountService,
        @inject(InjectionToken.SubscriptionsProvider) private readonly _subscriptionsProvider: SubscriptionsProvider
    ) {}

    async execute(event: NewOrganizationEvent): Promise<void> {
        let createdOrganization: any = null;
        const createdUsers: any = [];
        let updatedSubscriptionsProviderLocation = false;

        try {
            createdOrganization = await this._organizationsRepository.create({
                data: {
                    name: event.organizationName,
                    subscriptionsProviderId: event.organizationProviderId,
                },
            });

            await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                subscriptionsProviderLocationId: event.organizationProviderId,
                malouRestaurantId: createdOrganization._id.toString(),
            });

            updatedSubscriptionsProviderLocation = true;

            for (const user of event.users) {
                const account: Partial<IUser> = {
                    email: user.email,
                    subscriptionsProviderId: user.email,
                    defaultLanguage: event.usersLang ?? ApplicationLanguage.EN,
                    organizationIds: [createdOrganization._id],
                    role: Role.MALOU_BASIC,
                    caslRole: UserCaslRole.OWNER,
                };
                const createdUser = await this._createNewAccountService.createAccount({
                    user: account,
                    verified: false,
                    sendConfirmEmail: true,
                });
                // const createdUser = await this._usersRepository.create({
                //     data: {
                //         email: user.email.toLowerCase(),
                //         subscriptionsProviderId: user.email,
                //         organizationIds: [createdOrganization._id],
                //         password: randomString(8),
                //         role: Role.MALOU_BASIC,
                //         caslRole: UserCaslRole.OWNER,
                //         defaultLanguage: event.usersLang ?? ApplicationLanguage.EN,
                //     },
                // });
                createdUsers.push(createdUser);
            }

            // const user: Partial<IUser> = {
            //     email: event.principalUserEmail,
            //     subscriptionsProviderId: event.principalUserEmail,
            //     defaultLanguage: event.principalUserLang,
            //     organizationIds: [createdOrganization._id],
            //     role: Role.MALOU_BASIC,
            //     caslRole: UserCaslRole.OWNER,
            // };

            // createdUser = await this._createNewAccountService.createAccount({
            //     user,
            //     verified: false,
            //     sendConfirmEmail: true,
            // });
        } catch (err) {
            if (createdOrganization) {
                await this._organizationsRepository.deleteOne({
                    filter: { _id: toDbId(createdOrganization._id.toString()) },
                });
            }
            if (createdUsers.length > 0) {
                await this._usersRepository.deleteMany({
                    filter: { _id: { $in: createdUsers.map((user) => toDbId(user._id.toString())) } },
                });
            }
            if (updatedSubscriptionsProviderLocation) {
                await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                    subscriptionsProviderLocationId: event.organizationProviderId,
                    malouRestaurantId: null,
                });
            }
            throw err;
        }
    }
}
